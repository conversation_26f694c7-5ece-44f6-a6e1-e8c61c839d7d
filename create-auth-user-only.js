const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Email and password for the new admin user
const email = '<EMAIL>'; // <PERSON>ANGE THIS TO YOUR DESIRED EMAIL
const password = 'Admin123!'; // CHANGE THIS TO YOUR DESIRED PASSWORD
const displayName = 'Admin User'; // CHANGE THIS TO YOUR DESIRED NAME

// Create the user in Firebase Authentication
admin.auth().createUser({
  email: email,
  password: password,
  displayName: displayName,
})
.then((userRecord) => {
  console.log('Successfully created user:', userRecord.uid);
  
  // Set custom claims to make the user an admin
  return admin.auth().setCustomUserClaims(userRecord.uid, {
    role: 'admin'
  });
})
.then(() => {
  console.log('Successfully set admin custom claims');
  console.log('');
  console.log('IMPORTANT: You must now manually create a document in Firestore:');
  console.log('1. Go to Firebase Console > Firestore Database');
  console.log('2. Create a document in the "users" collection with the ID shown above');
  console.log('3. Add the following fields:');
  console.log('   - username: String (e.g., "' + email.split('@')[0] + '")');
  console.log('   - name: String (e.g., "' + displayName + '")');
  console.log('   - role: Number (1 for admin)');
  console.log('   - email: String ("' + email + '")');
  console.log('   - phoneNumber: String (can be empty)');
  console.log('   - status: Number (0 for active)');
  console.log('   - position: String (e.g., "Administrator")');
  console.log('   - department: String (e.g., "Administration")');
  console.log('   - lastActive: Timestamp (current time)');
  console.log('   - handledAlarmIds: Array (empty)');
  console.log('   - stores: Array (empty)');
  console.log('');
  console.log('You can now log in to the app with:');
  console.log(`Email: ${email}`);
  console.log(`Password: ${password}`);
  
  // Exit the process
  process.exit(0);
})
.catch((error) => {
  console.error('Error creating admin user:', error);
  process.exit(1);
});
