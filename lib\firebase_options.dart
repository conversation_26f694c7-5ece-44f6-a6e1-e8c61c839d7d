// File generated by FlutterFire CLI.
// This file should be replaced with the actual firebase_options.dart file
// generated by the FlutterFire CLI after you've connected your Firebase project.

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }



  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC-IR1pDb7T_Np5I0BPORu1M96JCMMNoVE',
    appId: '1:143942673732:android:67970aa70c518e4fbce311',
    messagingSenderId: '143942673732',
    projectId: 'larmknapp-52953',
    storageBucket: 'larmknapp-52953.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAh-nSvRVJPwo83bf9R_2EhD_SBL1guJRE',
    appId: '1:143942673732:ios:3a04f60e44419076bce311',
    messagingSenderId: '143942673732',
    projectId: 'larmknapp-52953',
    storageBucket: 'larmknapp-52953.firebasestorage.app',
    iosBundleId: 'com.security.larmknapp',
  );

}