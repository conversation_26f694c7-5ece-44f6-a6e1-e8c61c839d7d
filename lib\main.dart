import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'screens/alarm_screen.dart';
import 'screens/report_screen.dart';
import 'screens/alarm_detail_screen.dart';
import 'screens/alert_detail_screen.dart';
import 'screens/statistics_screen.dart';
import 'screens/login_screen.dart';
import 'screens/admin/admin_dashboard.dart';
import 'screens/guard/guard_dashboard.dart';
import 'screens/customer/customer_dashboard.dart';
import 'screens/guard/active_guards_screen.dart';
import 'screens/guard/device_status_screen.dart';
import 'screens/guard/store_search_screen.dart';
import 'screens/admin/user_management_screen.dart';
import 'screens/customer/guard_orders_screen.dart';
import 'screens/admin/manage_guard_orders_screen.dart';
import 'services/auth_service.dart';
import 'services/global_alert_listener.dart';
import 'theme/security_theme.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize AuthService
  final authService = AuthService();
  authService.initialize();

  // Initialize GlobalAlertListener to start listening for alerts immediately
  final alertListener = GlobalAlertListener();

  // Start listening for alerts with a slight delay to ensure Firebase is fully initialized
  // Use a longer delay to ensure everything is properly initialized
  Future.delayed(const Duration(seconds: 5), () {
    debugPrint("Starting global alert listener from main");
    alertListener.startListening();
  });

  // IoT simulator disabled - using real data from Firestore
  // final iotSimulator = IoTDeviceSimulator();
  // iotSimulator.stopSimulation();
  // iotSimulator.startSimulation();

  runApp(const ParatusApp());
}

class ParatusApp extends StatefulWidget {
  const ParatusApp({super.key});

  @override
  State<ParatusApp> createState() => _ParatusAppState();
}

class _ParatusAppState extends State<ParatusApp> {
  final AuthService _authService = AuthService();
  final GlobalAlertListener _alertListener = GlobalAlertListener();
  StreamSubscription? _authSubscription;

  @override
  void initState() {
    super.initState();

    // Set up the global alert listener to show notifications from anywhere in the app
    // First stop any existing listeners
    _alertListener.stopListening();

    // Set up the global alert listener with a longer delay to ensure the app is fully initialized
    Future.delayed(const Duration(seconds: 3), () {
      debugPrint("Starting global alert listener from app state");
      _alertListener.startListening();
    });

    // Lyssna på ändringar i autentiseringsstatus
    _authSubscription = _authService.authStateChanges.listen((user) {
      if (mounted) {
        setState(() {});

        // If user logged in, make sure the global alert listener is active
        if (user != null) {
          // Stop any existing listeners first
          _alertListener.stopListening();

          // Set up the global alert listener with a longer delay
          Future.delayed(const Duration(seconds: 2), () {
            debugPrint("Starting global alert listener after login");
            _alertListener.startListening();
          });
        }

        // IoT simulator disabled - using real data from Firestore
        // if (user == null) {
        //   Future.delayed(const Duration(milliseconds: 500), () {
        //     final iotSimulator = IoTDeviceSimulator();
        //     if (!iotSimulator.isSimulating) {
        //       iotSimulator.startSimulation();
        //     }
        //   });
        // }
      }
    });
  }

  @override
  void dispose() {
    // Avsluta prenumerationen när appen stängs
    _authSubscription?.cancel();
    _alertListener.stopListening();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: AuthService.navigatorKey, // Använd navigatorKey från AuthService
      debugShowCheckedModeBanner: false,
      title: 'Paratus Vakt',
      theme: SecurityTheme.themeData(),
      home: _authService.isLoggedIn
          ? _authService.isAdmin
              ? const AdminDashboard()
              : _authService.isGuard
                  ? const GuardDashboard()
                  : const CustomerDashboard()
          : LoginScreen(
              onLoginSuccess: (_) {
                setState(() {});
              },
            ),
      routes: {
        '/login': (context) => LoginScreen(
              onLoginSuccess: (_) {
                setState(() {});
              },
            ),
        '/admin_dashboard': (context) => const AdminDashboard(),
        '/guard_dashboard': (context) => const GuardDashboard(),
        '/customer_dashboard': (context) => const CustomerDashboard(),
        '/alarm_detail': (context) {
          final args = ModalRoute.of(context)!.settings.arguments as String;
          return AlarmDetailScreen(alarmId: args);
        },
        '/alert_detail': (context) {
          final args = ModalRoute.of(context)!.settings.arguments as String;
          return AlertDetailScreen(alertId: args);
        },
        '/alarms': (context) => const AlarmScreen(),
        '/reports': (context) => const ReportScreen(),
        '/statistics': (context) => const StatisticsScreen(),
        '/active_guards': (context) => const ActiveGuardsScreen(),
        '/user_management': (context) => const UserManagementScreen(),
        '/device_status': (context) => const DeviceStatusScreen(),
        '/store_search': (context) => const StoreSearchScreen(),
        '/guard_orders': (context) {
          final args = ModalRoute.of(context)!.settings.arguments as String;
          return GuardOrdersScreen(storeId: args);
        },
        '/manage_guard_orders': (context) => const ManageGuardOrdersScreen(),
      },
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromARGB(255, 23, 166, 214),
      child: const Center(
        child: Text(
          'Paratus',
          style: TextStyle(fontSize: 20, color: Colors.white),
        ),
      ),
    );
  }
}

class TeamScreen extends StatelessWidget {
  const TeamScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Här visas ditt team.',
        style: TextStyle(fontSize: 20),
      ),
    );
  }
}

class TasksScreen extends StatelessWidget {
  const TasksScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Här listas dina uppgifter.',
        style: TextStyle(fontSize: 20),
      ),
    );
  }
}

// AlarmScreen har flyttats till lib/screens/alarm_screen.dart

// ReportScreen har flyttats till lib/screens/report_screen.dart

class ScheduleScreen extends StatelessWidget {
  ScheduleScreen({super.key});

  // Real schedule data will be fetched from Firestore
  final List<Map<String, dynamic>> schedule = [];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: schedule.length,
      itemBuilder: (context, index) {
        final day = schedule[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${day['day']} • ${day['date']}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...day['shifts'].map<Widget>((shift) {
              final time = shift['time'];
              final isFree = time.toLowerCase() == 'ledig';
              return Card(
                color: isFree ? Colors.grey[200] : Colors.blue[50],
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: Icon(isFree ? Icons.free_breakfast : Icons.schedule),
                  title: Text(time),
                  subtitle: isFree
                      ? const Text('Ingen tjänst')
                      : Text('${shift['location']} – ${shift['role']}'),
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}
