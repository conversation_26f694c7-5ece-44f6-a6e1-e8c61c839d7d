import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alarm.dart';
import '../models/alert.dart';
import 'audio_service.dart';
import 'database_service.dart';
import 'auth_service.dart';

/// A service that listens for new alerts globally and shows notifications
/// regardless of where the user is in the app.
class GlobalAlertListener {
  static final GlobalAlertListener _instance = GlobalAlertListener._internal();
  final DatabaseService _databaseService = DatabaseService();
  final AudioService _audioService = AudioService();

  // Subscription for Firestore alerts
  StreamSubscription? _firestoreAlertsSubscription;

  // Flag to track if the listener is active
  bool _isListening = false;

  factory GlobalAlertListener() {
    return _instance;
  }

  GlobalAlertListener._internal();

  /// Start listening for new alerts
  void startListening() {
    // Don't start if already listening
    if (_isListening) {
      debugPrint("ALERT LISTENER: Already listening, not starting again");
      return;
    }

    debugPrint("ALERT LISTENER: Starting to listen for new alerts");

    // Set the flag
    _isListening = true;

    // Cancel any existing subscription
    _firestoreAlertsSubscription?.cancel();

    // Set up a direct listener to Firestore for alerts and specifically detect new documents
    _firestoreAlertsSubscription = _databaseService.getAlertsStream().listen((snapshot) {
      debugPrint("ALERT LISTENER: Received snapshot with ${snapshot.docChanges.length} changes");

      // Process only new documents that were added
      for (var change in snapshot.docChanges) {
        // Only process documents that were added (not modified or removed)
        if (change.type == DocumentChangeType.added) {
          final doc = change.doc;
          final data = doc.data() as Map<String, dynamic>;

          // Get the timestamp to check if this is a very recent alert
          final pressedAt = (data['pressedAt'] as Timestamp).toDate();
          final now = DateTime.now();
          final difference = now.difference(pressedAt);

          // Only process very recent alerts (created in the last 30 seconds)
          // This helps avoid showing notifications for older alerts when the app starts
          if (difference.inSeconds <= 30) {
            debugPrint("ALERT LISTENER: Processing new alert ${doc.id} (${difference.inSeconds} seconds old)");

            // Convert the alert to an alarm
            final alert = Alert.fromMap({
              'id': doc.id,
              ...data,
            });

            // Create an alarm from the alert
            final alarm = _convertAlertToAlarm(alert);

            // Show the notification immediately without requiring user interaction
            _showAlarmNotification(alarm);
          } else {
            debugPrint("ALERT LISTENER: Skipping older alert ${doc.id} (${difference.inSeconds} seconds old)");
          }
        }
      }
    });
  }

  /// Stop listening for new alerts
  void stopListening() {
    _firestoreAlertsSubscription?.cancel();
    _firestoreAlertsSubscription = null;
    _isListening = false;
  }

  /// Convert an Alert to an Alarm
  Alarm _convertAlertToAlarm(Alert alert) {
    return Alarm(
      id: alert.id,
      timestamp: alert.pressedAt,
      deviceId: alert.buttonId,
      location: alert.storeId, // Use storeId as location for now
      type: alert.alertType == AlertType.red ? AlarmType.red : AlarmType.yellow,
      status: AlarmStatus.new_,
    );
  }

  /// Show a notification for an alarm using a more reliable approach that doesn't require user interaction
  void _showAlarmNotification(Alarm alarm) {
    debugPrint("ALERT LISTENER: Showing notification for alarm ${alarm.id}");

    // Play alarm sound immediately
    _audioService.playAlarmSound(alarm.type);

    // Format the timestamp
    String formatDateTime(DateTime dateTime) {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
             '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }

    // Vibrate the device if possible
    HapticFeedback.heavyImpact();

    // Use the global navigator key to get the context
    // This ensures we can show dialogs from anywhere in the app
    final navigatorContext = AuthService.navigatorKey.currentContext;
    if (navigatorContext == null) {
      debugPrint("ALERT LISTENER: No navigator context available to show notification");
      return;
    }

    // Make sure we're on the main UI thread
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint("ALERT LISTENER: About to show dialog notification");

      // Force another sound to ensure it gets attention
      _audioService.playAlarmSound(alarm.type);

      // Use showDialog with useRootNavigator to ensure it appears on top of everything
      // This will show the dialog regardless of where the user is in the app
      showDialog(
        context: navigatorContext,
        barrierDismissible: false,
        useRootNavigator: true,
        builder: (BuildContext dialogContext) {
          return PopScope(
            // Prevent back button from dismissing the dialog
            canPop: false,
            child: AlertDialog(
              backgroundColor: alarm.type == AlarmType.red ? Colors.red.shade50 : Colors.orange.shade50,
              title: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                    size: 36,
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      alarm.type == AlarmType.red ? 'AKUT LARM' : 'VARNINGSLARM',
                      style: TextStyle(
                        color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Plats: ${alarm.location}',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Tid: ${formatDateTime(alarm.timestamp)}',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Ett larm har utlösts. Vänligen kontrollera situationen omedelbart.',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: const Text('Stäng'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    Navigator.of(navigatorContext).pushNamed('/alarm_detail', arguments: alarm.id);
                  },
                  child: const Text('Visa detaljer'),
                ),
              ],
            ),
          );
        },
      );

      // Play sound again after a short delay to ensure it gets attention
      Future.delayed(const Duration(milliseconds: 500), () {
        _audioService.playAlarmSound(alarm.type);
        HapticFeedback.heavyImpact();
      });
    });
  }
}
