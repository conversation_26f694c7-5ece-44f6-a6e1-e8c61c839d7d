import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/alarm.dart';
import '../models/alert.dart';
import '../models/store.dart';
import '../models/notification.dart';
import 'audio_service.dart';
import 'database_service.dart';
import 'auth_service.dart';
import 'alarm_service.dart';
import 'store_service.dart';
import 'alert_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  final AudioService _audioService = AudioService();
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final AlarmService _alarmService = AlarmService();
  final StoreService _storeService = StoreService();
  final AlertService _alertService = AlertService();

  final _notificationStreamController = StreamController<AppNotification>.broadcast();
  Stream<AppNotification> get notificationStream => _notificationStreamController.stream;

  // Subscriptions for global listeners
  StreamSubscription? _globalAlarmSubscription;
  StreamSubscription? _globalAlertSubscription;
  StreamSubscription? _firestoreAlertsSubscription;

  factory NotificationService() {
    return _instance;
  }

  NotificationService._internal();

  // Format date time for display
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Visa en fullskärmsnotifikation för inkommande larm
  void showAlarmNotification(BuildContext context, Alarm alarm) {
    // Spela upp larmljud baserat på larmtyp
    _audioService.playAlarmSound(alarm.type);

    // Use a microtask to avoid BuildContext across async gaps warning
    Future.microtask(() {
      // Check if the context is still valid
      if (context.mounted) {
        // Visa endast en fullskärmsnotifikation (ingen toast)
        showFullScreenNotification(context, alarm);
      }
    });
  }

  // Visa en fullskärmsnotifikation som användaren måste bekräfta
  void showFullScreenNotification(BuildContext context, Alarm alarm) {
    debugPrint("ACTUALLY SHOWING DIALOG FOR ALARM: ${alarm.id}");

    // Force vibration and sound for maximum attention
    _audioService.playAlarmSound(alarm.type);
    HapticFeedback.heavyImpact();

    // Always use the global navigator key to ensure we can show dialogs from anywhere in the app
    final navigatorContext = AuthService.navigatorKey.currentContext;
    if (navigatorContext == null) {
      debugPrint("No valid navigator context available to show notification");
      return;
    }

    // Show the dialog on the main UI thread
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showDialog(
        context: navigatorContext,
        barrierDismissible: false,
        useRootNavigator: true, // Use the root navigator to show on top of everything
        builder: (dialogContext) => PopScope(
          // Prevent back button from dismissing the dialog
          canPop: false,
          child: AlertDialog(
            backgroundColor: alarm.type == AlarmType.red ? Colors.red.shade50 : Colors.orange.shade50,
            title: Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                  size: 36,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    alarm.type == AlarmType.red ? 'AKUT LARM!' : 'VARNINGSLARM!',
                    style: TextStyle(
                      color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Larm mottaget från:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: alarm.type == AlarmType.red ? Colors.red.shade800 : Colors.orange.shade800,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Enhet: ${alarm.deviceId}',
                  style: const TextStyle(fontSize: 16),
                ),
                Text(
                  'Plats: ${alarm.location}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  'Tid: ${_formatDateTime(alarm.timestamp)}',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 20),
                Text(
                  'Larmtyp: ${alarm.type == AlarmType.red ? 'RÖD - AKUT' : 'GUL - VARNING'}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _audioService.stopAlarmSound();
                  Navigator.of(dialogContext).pop();
                },
                child: const Text('Stäng'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: alarm.type == AlarmType.red ? Colors.red : Colors.orange,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {
                  _audioService.stopAlarmSound();
                  Navigator.of(dialogContext).pop();
                  Navigator.pushNamed(
                    navigatorContext,
                    '/alarm_detail',
                    arguments: alarm.id,
                  );
                },
                child: const Text('Visa detaljer'),
              ),
            ],
          ),
        ),
      );

      // Play sound again after a short delay to ensure it gets attention
      Future.delayed(const Duration(milliseconds: 500), () {
        _audioService.playAlarmSound(alarm.type);
        HapticFeedback.heavyImpact();
      });
    });
  }
  }



  // Get notifications for the current user
  Stream<List<AppNotification>> getUserNotificationsStream() {
    final currentUser = _authService.currentUser;
    if (currentUser == null) {
      return Stream.value([]);
    }

    return _databaseService.getUserNotificationsStream(currentUser.id).map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return AppNotification.fromMap({
          'id': doc.id,
          ...data,
        });
      }).toList();
    });
  }

  // Create a notification
  Future<AppNotification?> createNotification({
    required String userId,
    String? alertId,
    String? storeId,
    String? storeName,
    required String title,
    required String message,
  }) async {
    try {
      final notificationData = {
        'userId': userId,
        'alertId': alertId,
        'storeId': storeId,
        'storeName': storeName,
        'title': title,
        'message': message,
        'createdAt': Timestamp.now(),
        'read': false,
      };

      final docRef = await _databaseService.addNotification(notificationData);

      // Get the created notification
      final doc = await _databaseService.notificationsCollection.doc(docRef.id).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      final notification = AppNotification.fromMap({
        'id': doc.id,
        ...data,
      });

      // Notify listeners
      _notificationStreamController.add(notification);

      return notification;
    } catch (e) {
      debugPrint('Error creating notification: $e');
      return null;
    }
  }

  // Mark a notification as read
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _databaseService.markNotificationAsRead(notificationId);
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Show a notification in the UI
  void showNotificationInUI(BuildContext context, AppNotification notification) {
    final snackBar = SnackBar(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification.title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(notification.message),
        ],
      ),
      action: notification.alertId != null
          ? SnackBarAction(
              label: 'View',
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/alert_detail',
                  arguments: notification.alertId,
                );
              },
            )
          : null,
      duration: const Duration(seconds: 5),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  // Show an alert notification
  void showAlertNotification(BuildContext context, Alert alert, Store store) {
    // Play sound based on alert status
    _audioService.playAlarmSound(alert.isActive ? AlarmType.red : AlarmType.yellow);

    final snackBar = SnackBar(
      backgroundColor: Colors.red,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ALERT',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          Text(
            'Button pressed at ${store.name}',
            style: const TextStyle(color: Colors.white),
          ),
        ],
      ),
      action: SnackBarAction(
        label: 'View',
        textColor: Colors.white,
        onPressed: () {
          Navigator.pushNamed(
            context,
            '/alert_detail',
            arguments: alert.id,
          );
        },
      ),
      duration: const Duration(seconds: 10),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  // Set up a global alarm listener that will show notifications from anywhere in the app
  void setupGlobalAlarmListener() {
    // Cancel any existing subscriptions first
    _globalAlarmSubscription?.cancel();
    _globalAlertSubscription?.cancel();
    _firestoreAlertsSubscription?.cancel();

    // Set up a subscription to the AlarmService stream (for backward compatibility)
    _globalAlarmSubscription = _alarmService.alarmStream.listen((alarm) {
      // Play alarm sound immediately
      _audioService.playAlarmSound(alarm.type);

      // Show the notification using a safer approach
      // We use addPostFrameCallback to ensure we're in the correct phase of the widget lifecycle
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final context = AuthService.navigatorKey.currentContext;
        if (context != null) {
          // Show the dialog directly
          showFullScreenNotification(context, alarm);
        }
      });
    });

    // Set up a direct listener to Firestore for alerts and specifically detect new documents
    // This is the most reliable way to get real-time alerts
    _firestoreAlertsSubscription = _databaseService.getAlertsStream().listen((snapshot) {
      debugPrint("NOTIFICATION SERVICE: Received snapshot with ${snapshot.docChanges.length} changes");

      // Process only new documents that were added
      for (var change in snapshot.docChanges) {
        // Only process documents that were added (not modified or removed)
        if (change.type == DocumentChangeType.added) {
          final doc = change.doc;
          final data = doc.data() as Map<String, dynamic>;

          // Get the timestamp to check if this is a very recent alert
          final pressedAt = (data['pressedAt'] as Timestamp).toDate();
          final now = DateTime.now();
          final difference = now.difference(pressedAt);

          // Only process very recent alerts (created in the last 30 seconds)
          if (difference.inSeconds <= 30) {
            debugPrint("NOTIFICATION SERVICE: Processing new alert ${doc.id} (${difference.inSeconds} seconds old)");

            // Convert the alert to an alarm
            final alert = Alert.fromMap({
              'id': doc.id,
              ...data,
            });

            // Create an alarm from the alert
            final alarm = _convertAlertToAlarm(alert);

            // Play alarm sound immediately
            _audioService.playAlarmSound(alarm.type);

            // Show the notification immediately without requiring user interaction
            // Use the global navigator key to get the context
            final navigatorContext = AuthService.navigatorKey.currentContext;
            if (navigatorContext != null) {
              // Show the dialog directly on the main UI thread
              WidgetsBinding.instance.addPostFrameCallback((_) {
                showFullScreenNotification(navigatorContext, alarm);
              });
            } else {
              debugPrint("NOTIFICATION SERVICE: No context available to show notification");
            }
          } else {
            debugPrint("NOTIFICATION SERVICE: Skipping older alert ${doc.id} (${difference.inSeconds} seconds old)");
          }
        }
      }
    });
  }



  // Convert an Alert to an Alarm
  Alarm _convertAlertToAlarm(Alert alert) {
    return Alarm(
      id: alert.id,
      timestamp: alert.pressedAt,
      deviceId: alert.buttonId,
      location: alert.storeId, // Use storeId as location for now
      type: alert.alertType == AlertType.red ? AlarmType.red : AlarmType.yellow,
      status: AlarmStatus.new_,
    );
  }

  // Stop all global listeners
  void stopGlobalAlarmListener() {
    _globalAlarmSubscription?.cancel();
    _globalAlarmSubscription = null;

    _globalAlertSubscription?.cancel();
    _globalAlertSubscription = null;

    _firestoreAlertsSubscription?.cancel();
    _firestoreAlertsSubscription = null;
  }

  // Dispose resources
  void dispose() {
    stopGlobalAlarmListener();
    _notificationStreamController.close();
  }
}
