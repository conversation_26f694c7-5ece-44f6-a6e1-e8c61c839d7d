<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Broker <PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 700px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .connection-status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .connecting {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .config {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .config label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .config input {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .config-row {
            display: flex;
            gap: 15px;
        }
        
        .config-row > div {
            flex: 1;
        }
        
        .button-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .alert-button {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .red-alert {
            background-color: #dc3545;
            color: white;
        }
        
        .red-alert:hover:not(:disabled) {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        
        .yellow-alert {
            background-color: #ffc107;
            color: #212529;
        }
        
        .yellow-alert:hover:not(:disabled) {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        
        .connect-button {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .connect-button:hover:not(:disabled) {
            background-color: #218838;
        }
        
        .disconnect-button {
            background-color: #dc3545;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .disconnect-button:hover:not(:disabled) {
            background-color: #c82333;
        }
        
        .alert-button:disabled, .connect-button:disabled, .disconnect-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            min-height: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .note {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📡 MQTT Broker Alert Sender</h1>
        
        <div class="note">
            <strong>Note:</strong> This page sends MQTT messages directly to your broker, simulating how your IoT buttons work. 
            Make sure your webhook is configured to receive messages from the broker.
        </div>
        
        <div id="connectionStatus" class="connection-status disconnected">
            🔴 Disconnected from MQTT Broker
        </div>
        
        <div class="config">
            <h3>MQTT Broker Configuration</h3>
            <div class="config-row">
                <div>
                    <label for="brokerHost">Broker Host:</label>
                    <input type="text" id="brokerHost" value="i2526225.ala.eu-central-1.emqxsl.com" readonly>
                </div>
                <div>
                    <label for="brokerPort">WebSocket Port:</label>
                    <input type="number" id="brokerPort" value="8084" readonly>
                </div>
            </div>
            
            <div class="config-row">
                <div>
                    <label for="username">Username:</label>
                    <input type="text" id="username" value="test">
                </div>
                <div>
                    <label for="password">Password:</label>
                    <input type="password" id="password" value="Test123.">
                </div>
            </div>
            
            <div class="config-row">
                <div>
                    <label for="clientId">Client ID:</label>
                    <input type="text" id="clientId" value="web-client-123">
                </div>
                <div style="display: flex; align-items: end; gap: 10px;">
                    <button id="connectBtn" class="connect-button" onclick="connectToMqtt()">Connect</button>
                    <button id="disconnectBtn" class="disconnect-button" onclick="disconnectFromMqtt()" disabled>Disconnect</button>
                </div>
            </div>
        </div>
        
        <div class="button-container">
            <button class="alert-button red-alert" id="redAlertBtn" onclick="sendAlert('red')" disabled>
                🔴 Send Red Alert
            </button>
            <button class="alert-button yellow-alert" id="yellowAlertBtn" onclick="sendAlert('yellow')" disabled>
                🟡 Send Yellow Alert
            </button>
        </div>
        
        <div id="feedback" class="feedback"></div>
    </div>

    <!-- Include Paho MQTT JavaScript client -->
    <script src="https://unpkg.com/paho-mqtt@1.1.0/paho-mqtt.js"></script>
    
    <script>
        let mqttClient = null;
        let isConnected = false;
        
        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const alertButtons = document.querySelectorAll('.alert-button');
            
            if (status === 'connected') {
                statusDiv.className = 'connection-status connected';
                statusDiv.innerHTML = '🟢 Connected to MQTT Broker';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                alertButtons.forEach(btn => btn.disabled = false);
                isConnected = true;
            } else if (status === 'connecting') {
                statusDiv.className = 'connection-status connecting';
                statusDiv.innerHTML = '🟡 Connecting to MQTT Broker...';
                connectBtn.disabled = true;
                disconnectBtn.disabled = true;
                alertButtons.forEach(btn => btn.disabled = true);
                isConnected = false;
            } else {
                statusDiv.className = 'connection-status disconnected';
                statusDiv.innerHTML = '🔴 Disconnected from MQTT Broker';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                alertButtons.forEach(btn => btn.disabled = true);
                isConnected = false;
            }
            
            if (message) {
                showFeedback(message, status === 'connected' ? 'success' : 'error');
            }
        }
        
        function connectToMqtt() {
            const host = document.getElementById('brokerHost').value;
            const port = parseInt(document.getElementById('brokerPort').value);
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const clientId = document.getElementById('clientId').value;
            
            updateConnectionStatus('connecting');
            showFeedback('<span class="spinner"></span>Connecting to MQTT broker...', 'info');
            
            try {
                // Create MQTT client
                mqttClient = new Paho.MQTT.Client(host, port, clientId);
                
                // Set callback handlers
                mqttClient.onConnectionLost = onConnectionLost;
                mqttClient.onMessageArrived = onMessageArrived;
                
                // Connect options
                const connectOptions = {
                    useSSL: true,
                    userName: username,
                    password: password,
                    onSuccess: onConnect,
                    onFailure: onConnectFailure,
                    timeout: 10,
                    keepAliveInterval: 30,
                };
                
                mqttClient.connect(connectOptions);
            } catch (error) {
                updateConnectionStatus('disconnected', `Connection error: ${error.message}`);
            }
        }
        
        function disconnectFromMqtt() {
            if (mqttClient && isConnected) {
                mqttClient.disconnect();
                updateConnectionStatus('disconnected', 'Disconnected from MQTT broker');
            }
        }
        
        function onConnect() {
            updateConnectionStatus('connected', 'Successfully connected to MQTT broker!');
        }
        
        function onConnectFailure(error) {
            updateConnectionStatus('disconnected', `Connection failed: ${error.errorMessage || 'Unknown error'}`);
        }
        
        function onConnectionLost(responseObject) {
            if (responseObject.errorCode !== 0) {
                updateConnectionStatus('disconnected', `Connection lost: ${responseObject.errorMessage}`);
            }
        }
        
        function onMessageArrived(message) {
            console.log('Message arrived:', message.destinationName, message.payloadString);
        }
        
        function sendAlert(alertType) {
            if (!isConnected || !mqttClient) {
                showFeedback('❌ Not connected to MQTT broker', 'error');
                return;
            }
            
            // Prepare message based on alert type
            const topic = alertType === 'red' ? '/store/Coop/button/red_alert' : '/store/Coop/button/yellow_alert';
            const payload = alertType === 'red' ? 'rod larm' : 'gul larm';
            
            try {
                const message = new Paho.MQTT.Message(payload);
                message.destinationName = topic;
                message.qos = 1;
                message.retained = false;
                
                mqttClient.send(message);
                
                showFeedback(
                    `✅ ${alertType.toUpperCase()} alert sent successfully!<br>` +
                    `Topic: ${topic}<br>` +
                    `Payload: ${payload}`, 
                    'success'
                );
            } catch (error) {
                showFeedback(`❌ Failed to send alert: ${error.message}`, 'error');
            }
        }
        
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
        }
        
        // Auto-connect on page load
        window.addEventListener('load', () => {
            showFeedback('Ready to connect to MQTT broker', 'info');
        });
    </script>
</body>
</html>
