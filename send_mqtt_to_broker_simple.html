<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT Broker <PERSON><PERSON> (Simple)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .note {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin-bottom: 20px;
        }
        
        .error-note {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
            margin-bottom: 20px;
        }
        
        .config {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .config label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .config input, .config textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .button-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .alert-button {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .red-alert {
            background-color: #dc3545;
            color: white;
        }
        
        .red-alert:hover {
            background-color: #c82333;
            transform: translateY(-2px);
        }
        
        .yellow-alert {
            background-color: #ffc107;
            color: #212529;
        }
        
        .yellow-alert:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }
        
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            min-height: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .download-link {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        
        .download-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📡 MQTT Broker Alert Sender</h1>
        
        <div class="error-note">
            <strong>MQTT Library Issue:</strong> The MQTT JavaScript library couldn't load from CDN. 
            <br><br>
            <strong>Solution:</strong> Download the Paho MQTT library manually:
            <br>
            1. Download: <a href="https://github.com/eclipse/paho.mqtt.javascript/releases/download/v1.1.0/paho-mqtt-js-1.1.0.zip" class="download-link" target="_blank">paho-mqtt-js-1.1.0.zip</a>
            <br>
            2. Extract the zip file
            <br>
            3. Copy <code>paho-mqtt.js</code> to the same folder as this HTML file
            <br>
            4. Refresh this page
        </div>
        
        <div class="note">
            <strong>Alternative:</strong> Use the <strong>send_alert_directly_to_cloud_function.html</strong> file instead, 
            which sends alerts directly to your Cloud Function without needing MQTT.
        </div>
        
        <div class="config">
            <h3>MQTT Message Configuration</h3>
            <p>Since we can't connect to MQTT directly, you can copy these message details to use with an MQTT client like MQTT Explorer or mosquitto_pub:</p>
            
            <label for="brokerInfo">Broker Information:</label>
            <textarea id="brokerInfo" rows="4" readonly>Host: i2526225.ala.eu-central-1.emqxsl.com
Port: 8883 (MQTT over TLS/SSL)
WebSocket Port: 8084 (for web clients)
Username: test
Password: Test123.</textarea>
            
            <label for="redAlertCmd">Red Alert Command (mosquitto_pub):</label>
            <textarea id="redAlertCmd" rows="3" readonly>mosquitto_pub -h i2526225.ala.eu-central-1.emqxsl.com -p 8883 --cafile emqxsl-ca.crt -u test -P "Test123." -t "/store/Coop/button/red_alert" -m "rod larm" -q 1</textarea>
            
            <label for="yellowAlertCmd">Yellow Alert Command (mosquitto_pub):</label>
            <textarea id="yellowAlertCmd" rows="3" readonly>mosquitto_pub -h i2526225.ala.eu-central-1.emqxsl.com -p 8883 --cafile emqxsl-ca.crt -u test -P "Test123." -t "/store/Coop/button/yellow_alert" -m "gul larm" -q 1</textarea>
        </div>
        
        <div class="button-container">
            <button class="alert-button red-alert" onclick="copyCommand('red')">
                📋 Copy Red Alert Command
            </button>
            <button class="alert-button yellow-alert" onclick="copyCommand('yellow')">
                📋 Copy Yellow Alert Command
            </button>
        </div>
        
        <div id="feedback" class="feedback"></div>
    </div>

    <script>
        function copyCommand(alertType) {
            const textArea = document.getElementById(alertType === 'red' ? 'redAlertCmd' : 'yellowAlertCmd');
            textArea.select();
            textArea.setSelectionRange(0, 99999); // For mobile devices
            
            try {
                document.execCommand('copy');
                showFeedback(`✅ ${alertType.toUpperCase()} alert command copied to clipboard!`, 'success');
            } catch (err) {
                showFeedback(`❌ Failed to copy command. Please select and copy manually.`, 'error');
            }
        }
        
        function showFeedback(message, type) {
            const feedback = document.getElementById('feedback');
            feedback.innerHTML = message;
            feedback.className = `feedback ${type}`;
        }
        
        // Check if paho-mqtt.js exists locally
        window.addEventListener('load', () => {
            const script = document.createElement('script');
            script.src = './paho-mqtt.js';
            script.onload = () => {
                showFeedback('✅ Local MQTT library found! You can now use the full MQTT functionality.', 'success');
                // Here you could enable the MQTT functionality
            };
            script.onerror = () => {
                showFeedback('ℹ️ Use the commands above with an MQTT client, or download the library for web functionality.', 'info');
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
