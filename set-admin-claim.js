const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// The user ID of the user you want to make an admin
// This is the UID that was output when you created the user
const userId = '5GyAIYVyvtcVe6MRBJAfV6JFdiM2';

// Set custom claims to make the user an admin
admin.auth().setCustomUserClaims(userId, {
  role: 'admin'
})
.then(() => {
  console.log('Successfully set admin custom claims for user:', userId);
  console.log('');
  console.log('IMPORTANT: You must now manually create a document in Firestore:');
  console.log('1. Go to Firebase Console > Firestore Database');
  console.log('2. Create a document in the "users" collection with this ID:', userId);
  console.log('3. Add the following fields:');
  console.log('   - username: String (choose a username)');
  console.log('   - name: String (e.g., "Admin User")');
  console.log('   - role: Number (1 for admin)');
  console.log('   - email: String (the email you used to create the user)');
  console.log('   - phoneNumber: String (can be empty)');
  console.log('   - status: Number (0 for active)');
  console.log('   - position: String (e.g., "Administrator")');
  console.log('   - department: String (e.g., "Administration")');
  console.log('   - lastActive: Timestamp (current time)');
  console.log('   - handledAlarmIds: Array (empty)');
  console.log('   - stores: Array (empty)');
  console.log('');
  console.log('After creating this document, you should be able to log in with the admin user.');
  
  // Exit the process
  process.exit(0);
})
.catch((error) => {
  console.error('Error setting admin custom claims:', error);
  process.exit(1);
});
